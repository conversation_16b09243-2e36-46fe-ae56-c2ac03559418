#!/usr/bin/env python3
"""
Test script for Project Synapse multi-agent system.
"""

import os
import sys
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Add the synapse module to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_basic_functionality():
    """Test basic functionality of the multi-agent system."""
    
    print("🚀 Testing Project Synapse Multi-Agent System")
    print("=" * 50)
    
    try:
        # Import the system
        from synapse.agent import ProjectSynapseSystem
        
        print("✅ Successfully imported ProjectSynapseSystem")
        
        # Initialize the system
        print("\n🔧 Initializing multi-agent system...")
        synapse_system = ProjectSynapseSystem()
        
        print("✅ Multi-agent system initialized successfully")
        print(f"📊 Registered agents: {len(synapse_system.communication.agent_registry)}")
        
        # Test a simple business query
        print("\n💼 Testing business analysis...")
        test_query = """
        I run a small home cleaning service in Delhi. I currently have 5 regular customers 
        and earn about ₹30,000 per month. I want to grow my business and reach more customers 
        online. I have a budget of ₹20,000 for improvements. Can you help me create a growth plan?
        """
        
        print(f"Query: {test_query[:100]}...")
        
        # Process the query
        result = synapse_system.process_business_query(test_query.strip())
        
        print("\n📋 Analysis Result:")
        print("-" * 30)
        print(result[:500] + "..." if len(result) > 500 else result)
        
        print("\n✅ Test completed successfully!")
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        print("Make sure all dependencies are installed: poetry install")
        
    except Exception as e:
        print(f"❌ Error during testing: {e}")
        print("\nTroubleshooting tips:")
        print("1. Check your Google Cloud authentication: gcloud auth application-default login")
        print("2. Verify your project ID in .env file")
        print("3. Ensure Vertex AI API is enabled in your Google Cloud project")


def test_individual_agents():
    """Test individual agents."""
    
    print("\n🔍 Testing Individual Agents")
    print("=" * 30)
    
    try:
        from synapse.utils.communication import AgentCommunication
        from synapse.agents.master_agent import MasterAgent
        
        # Test communication system
        comm = AgentCommunication()
        print("✅ Communication system initialized")
        
        # Test master agent
        master = MasterAgent(comm)
        print("✅ Master Agent initialized")
        
        # Test query analysis
        test_query = "I need help growing my restaurant business in Mumbai"
        analysis = master.analyze_user_query(test_query)
        
        print(f"✅ Query analysis completed")
        print(f"📊 Business type identified: {analysis.get('business_type', 'Unknown')}")
        print(f"📊 Agents required: {len(analysis.get('agents_required', []))}")
        
    except Exception as e:
        print(f"❌ Error testing individual agents: {e}")


if __name__ == "__main__":
    print("🧪 Project Synapse Test Suite")
    print("=" * 40)
    
    # Check environment
    print("\n🔧 Environment Check:")
    project_id = os.getenv("GOOGLE_CLOUD_PROJECT")
    location = os.getenv("GOOGLE_CLOUD_LOCATION")
    use_vertex = os.getenv("GOOGLE_GENAI_USE_VERTEXAI")
    
    print(f"Google Cloud Project: {project_id}")
    print(f"Location: {location}")
    print(f"Use Vertex AI: {use_vertex}")
    
    if not project_id:
        print("⚠️  Warning: GOOGLE_CLOUD_PROJECT not set in environment")
    
    # Run tests
    test_individual_agents()
    test_basic_functionality()
    
    print("\n🎉 Test suite completed!")
    print("\nTo run the full system:")
    print("1. poetry shell")
    print("2. adk web")
    print("3. Open http://localhost:8000 and select 'synapse'")
