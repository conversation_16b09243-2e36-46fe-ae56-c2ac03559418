# Project Synapse

A sophisticated multi-agent AI system designed to provide strategic business analysis and actionable growth plans for local service businesses in India.

## Overview

Project Synapse leverages Google's Agent Development Kit (ADK) and Vertex AI to coordinate a team of specialized AI agents that collaborate to deliver comprehensive, reliable, and highly contextualized business recommendations.

## Features

- **Multi-Agent Architecture**: 8 specialized AI agents working together
- **Indian Market Focus**: Tailored for local service businesses in India
- **Comprehensive Analysis**: Market research, financial analysis, technical feasibility
- **Actionable Plans**: Multi-stage growth plans with ROI projections
- **Cultural Context**: Considers Indian business environment and cultural factors

## Agent Roster

| Agent | Role | Capabilities |
|-------|------|-------------|
| **Master Agent** | Central coordinator | Query parsing, task delegation, synthesis |
| **Research Agent** | Market intelligence | Google Search, competitor analysis, trend research |
| **Financial Agent** | Financial analysis | ROI projections, pricing strategy, budget optimization |
| **UI/UX Designer Agent** | User experience | Website analysis, mobile optimization, conversion optimization |
| **Feasibility Agent** | Implementation assessment | Viability analysis, resource planning, risk assessment |
| **Question Answering Agent** | Query resolution | Context-based answers, fact verification |
| **AI Development Agent** | Technical solutions | Technology recommendations, automation strategies |
| **Content Writer Agent** | Marketing content | Website copy, social media strategy, email campaigns |

## Prerequisites

- Python 3.11+
- Poetry for dependency management
- Google Cloud Project with Vertex AI enabled
- Google Cloud CLI installed and configured

## Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd synapse
   ```

2. **Install Poetry** (if not already installed)
   ```bash
   pip install poetry
   ```

3. **Install dependencies**
   ```bash
   poetry install
   ```

4. **Set up Google Cloud credentials**
   ```bash
   gcloud auth application-default login
   gcloud auth application-default set-quota-project my-first-project
   ```

5. **Configure environment variables**
   
   Update the `.env` file with your Google Cloud project details:
   ```env
   GOOGLE_CLOUD_PROJECT="my-first-project"
   GOOGLE_CLOUD_LOCATION="us-central1"
   GOOGLE_GENAI_USE_VERTEXAI="True"
   ```

## Usage

### Running with ADK Web Interface

1. **Activate the virtual environment**
   ```bash
   poetry shell
   ```

2. **Start the ADK web interface**
   ```bash
   adk web
   ```

3. **Open your browser**
   
   Navigate to `http://localhost:8000` and select "synapse" from the dropdown.

### Running with ADK CLI

```bash
adk run synapse
```

## Example Interactions

### Business Analysis Request

```
User: I run a home cleaning service in Mumbai. I want to grow my business online and increase my customer base. My current monthly revenue is ₹50,000 and I have a budget of ₹25,000 for improvements.

System: [Analyzes business through multi-agent system and provides comprehensive growth plan]
```

### Specific Question

```
User: What's the best pricing strategy for home cleaning services in Indian metros?

System: [Research Agent gathers market data, Financial Agent analyzes pricing, provides specific recommendations]
```

## System Architecture

```
User Query
    ↓
Master Agent (Coordinator)
    ↓
┌─────────────────────────────────────────────────────────┐
│  Specialized Agents (Parallel Processing)              │
├─────────────┬─────────────┬─────────────┬─────────────┤
│ Research    │ Financial   │ UI/UX       │ Feasibility │
│ Agent       │ Agent       │ Agent       │ Agent       │
├─────────────┼─────────────┼─────────────┼─────────────┤
│ QA Agent    │ AI Dev      │ Content     │             │
│             │ Agent       │ Writer      │             │
└─────────────┴─────────────┴─────────────┴─────────────┘
    ↓
Inter-Agent Communication & Data Sharing
    ↓
Master Agent (Synthesis)
    ↓
Comprehensive Business Growth Plan
```

## Output Format

The system provides structured business plans in markdown format:

- **Executive Summary**
- **Stage 1: Immediate Wins (0-30 days)**
- **Stage 2: Foundational Growth (1-6 months)**
- **Stage 3: Scaling Up (6+ months)**
- **ROI Projections and Success Metrics**
- **Implementation Support**

## Indian Market Specialization

- **Currency**: All financial projections in INR
- **Cultural Context**: Considers Indian business practices and customer behavior
- **Local Platforms**: Integration with Indian payment gateways, WhatsApp Business
- **Regional Variations**: Accounts for differences across Indian cities and states
- **Language Support**: English and Hindi content strategies
- **Regulatory Compliance**: Indian business regulations and tax considerations

## Development

### Project Structure

```
synapse/
├── agents/                 # Specialized AI agents
│   ├── master_agent.py    # Central coordinator
│   ├── research_agent.py  # Market research
│   ├── financial_agent.py # Financial analysis
│   └── ...
├── utils/                 # Utility modules
│   ├── communication.py   # Inter-agent communication
│   ├── tools.py          # External tools (Search, Web)
│   └── config.py         # Configuration management
├── agent.py              # Main ADK agent entry point
└── __init__.py
```

### Adding New Agents

1. Create new agent class inheriting from `BaseAgent`
2. Implement required methods (`get_specific_instructions`, `process_request`)
3. Register agent in `agents/__init__.py`
4. Add to `ProjectSynapseSystem` in `agent.py`

### Testing

```bash
poetry run pytest
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Submit a pull request

## License

This project is licensed under the Apache 2.0 License.

## Support

For questions or issues:
1. Check the documentation
2. Review existing issues
3. Create a new issue with detailed description

## Roadmap

- [ ] Enhanced A2A protocol integration
- [ ] Real-time business monitoring
- [ ] Integration with Indian business platforms
- [ ] Advanced analytics and reporting
- [ ] Mobile app interface
- [ ] Multi-language support expansion
