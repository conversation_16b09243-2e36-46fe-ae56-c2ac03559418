"""
Question Answering Agent - Answers questions based on context.
"""

import json
from typing import Dict, Any, Optional
from .base_agent import BaseAgent
from ..utils.communication import AgentMessage


class QuestionAnsweringAgent(BaseAgent):
    """
    Question Answering Agent that answers questions based on provided context
    from user and other agents, ensuring factual accuracy.
    """
    
    def __init__(self, communication):
        super().__init__(
            name="Question Answering Agent",
            description="Answers questions based on context from user and other agents, ensuring factual accuracy and Indian market relevance",
            communication=communication
        )
    
    def get_specific_instructions(self) -> str:
        return """
        As the Question Answering Agent, your responsibilities include:
        
        1. CONTEXTUAL ANSWERS: Provide accurate answers based on available context
        2. FACT VERIFICATION: Ensure all answers are grounded in verified data
        3. CLARIFICATION: Ask for clarification when questions are ambiguous
        4. SYNTHESIS: Combine information from multiple sources for comprehensive answers
        5. INDIAN CONTEXT: Ensure answers are relevant to Indian market conditions
        6. FOLLOW-UP: Suggest related questions or additional information
        
        ANSWER QUALITY STANDARDS:
        - Base answers on verified research data
        - Cite sources when possible
        - Acknowledge limitations and uncertainties
        - Provide specific, actionable information
        - Use Indian examples and case studies
        - Include relevant metrics in INR
        
        QUESTION TYPES TO HANDLE:
        - Market research questions
        - Financial and ROI queries
        - Implementation and feasibility questions
        - Technical and operational queries
        - Strategy and planning questions
        - Competitive analysis questions
        
        Always provide helpful, accurate, and contextually relevant answers.
        """
    
    def answer_question(self, question: str, context: Dict[str, Any]) -> Dict[str, Any]:
        """Answer a question based on available context."""
        
        # Get all available context from shared state
        all_context = self.get_shared_state()
        
        answer_prompt = f"""
        Answer this question based on the available context:
        
        Question: {question}
        
        Available Context:
        {json.dumps(context, indent=2)}
        
        Additional Context from Analysis:
        {json.dumps(all_context, indent=2)}
        
        Provide a comprehensive answer that:
        1. Directly addresses the question
        2. Uses specific data and examples from the context
        3. Includes relevant Indian market insights
        4. Provides actionable recommendations
        5. Cites sources or analysis when possible
        6. Acknowledges any limitations in available data
        
        If the question cannot be fully answered with available context, 
        clearly state what additional information would be needed.
        
        Format the answer clearly with headings and bullet points where appropriate.
        """
        
        answer = self.generate_response(answer_prompt)
        
        return {
            "question": question,
            "answer": answer,
            "context_used": list(context.keys()),
            "confidence": "high" if all_context else "medium"
        }
    
    def receive_message(self, message: AgentMessage):
        """Handle messages from other agents."""
        
        if message.message_type == "request":
            # Process question answering request
            task = message.content
            context = message.context or {}
            
            # Answer the question
            qa_results = self.process_request(task, context)
            
            # Send results back to requesting agent
            self.send_message_to_agent(
                recipient=message.sender,
                message_type="response",
                content=qa_results,
                context={"original_request": task}
            )
    
    def process_request(self, request: str, context: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """Process a question answering request."""
        
        try:
            # Extract question from request
            question = request
            if "question:" in request.lower():
                question = request.split("question:", 1)[1].strip()
            
            # Get comprehensive context
            full_context = context or {}
            
            # Answer the question
            qa_result = self.answer_question(question, full_context)
            
            return {
                "status": "success",
                "qa_data": qa_result,
                "question": question
            }
            
        except Exception as e:
            return {
                "status": "error",
                "error": str(e),
                "message": "Error answering question"
            }
