"""
Feasibility Agent - Assesses practicality and viability of proposed solutions.
"""

import json
from typing import Dict, Any, Optional, List
from .base_agent import BaseAgent
from ..utils.communication import AgentMessage


class FeasibilityAgent(BaseAgent):
    """
    Feasibility Agent that assesses the practicality and viability of proposed solutions
    with focus on Indian market constraints and opportunities.
    """
    
    def __init__(self, communication):
        super().__init__(
            name="Feasibility Agent",
            description="Assesses practicality, viability, and implementation feasibility of proposed business solutions for Indian market",
            communication=communication
        )
    
    def get_specific_instructions(self) -> str:
        return """
        As the Feasibility Agent, your responsibilities include:
        
        1. VIABILITY ASSESSMENT: Evaluate if proposed solutions are realistic and achievable
        2. RESOURCE ANALYSIS: Assess required resources (time, money, skills, technology)
        3. RISK EVALUATION: Identify potential risks and mitigation strategies
        4. TIMELINE PLANNING: Create realistic implementation timelines
        5. CONSTRAINT IDENTIFICATION: Identify limitations and bottlenecks
        6. ALTERNATIVE SOLUTIONS: Suggest more feasible alternatives when needed
        
        INDIAN MARKET CONSTRAINTS:
        - Limited budgets for SMEs
        - Skill availability and training needs
        - Technology infrastructure limitations
        - Regulatory compliance requirements
        - Cultural and language barriers
        - Regional market variations
        
        FEASIBILITY CRITERIA:
        - Technical feasibility (can it be done?)
        - Economic feasibility (is it cost-effective?)
        - Operational feasibility (can it be maintained?)
        - Schedule feasibility (realistic timelines?)
        - Legal feasibility (compliance with Indian laws?)
        - Market feasibility (will customers accept it?)
        
        Always provide realistic assessments with practical alternatives.
        """
    
    def assess_technical_feasibility(self, proposed_solutions: Dict[str, Any], business_context: Dict[str, Any]) -> Dict[str, Any]:
        """Assess technical feasibility of proposed solutions."""
        
        feasibility_prompt = f"""
        Assess the technical feasibility of these proposed solutions for an Indian business:
        
        Proposed Solutions:
        {json.dumps(proposed_solutions, indent=2)}
        
        Business Context:
        {json.dumps(business_context, indent=2)}
        
        Evaluate each solution for:
        
        1. TECHNICAL REQUIREMENTS:
        - Technology stack needed
        - Infrastructure requirements
        - Integration complexity
        - Maintenance requirements
        
        2. SKILL REQUIREMENTS:
        - Technical skills needed
        - Training requirements
        - Availability of skilled resources in India
        - Cost of acquiring skills
        
        3. IMPLEMENTATION COMPLEXITY:
        - Development time estimates
        - Testing and deployment phases
        - Risk factors and dependencies
        - Scalability considerations
        
        4. INDIAN MARKET FACTORS:
        - Internet infrastructure requirements
        - Mobile compatibility needs
        - Local technology preferences
        - Regulatory compliance needs
        
        Rate each solution as: HIGH FEASIBILITY, MEDIUM FEASIBILITY, or LOW FEASIBILITY
        Provide specific recommendations for improving feasibility.
        """
        
        technical_assessment = self.generate_response(feasibility_prompt)
        
        return {
            "technical_feasibility": technical_assessment,
            "assessment_criteria": ["technology", "skills", "complexity", "market_factors"]
        }
    
    def assess_economic_feasibility(self, financial_projections: Dict[str, Any], budget_constraints: Dict[str, Any]) -> Dict[str, Any]:
        """Assess economic feasibility and cost-effectiveness."""
        
        economic_prompt = f"""
        Assess the economic feasibility of proposed strategies:
        
        Financial Projections:
        {json.dumps(financial_projections, indent=2)}
        
        Budget Constraints:
        {json.dumps(budget_constraints, indent=2)}
        
        Evaluate:
        
        1. COST-BENEFIT ANALYSIS:
        - Initial investment requirements
        - Ongoing operational costs
        - Expected returns and timeline
        - Break-even analysis
        
        2. BUDGET ALIGNMENT:
        - Fit within available budget
        - Financing options for shortfalls
        - Phased implementation possibilities
        - Cost optimization opportunities
        
        3. RISK-RETURN PROFILE:
        - Financial risk assessment
        - Sensitivity analysis
        - Worst-case scenario planning
        - Contingency budget requirements
        
        4. INDIAN MARKET ECONOMICS:
        - Local cost structures
        - Price sensitivity considerations
        - Payment terms and cash flow
        - Currency and inflation factors
        
        Provide feasibility rating and recommendations for cost optimization.
        """
        
        economic_assessment = self.generate_response(economic_prompt)
        
        return {
            "economic_feasibility": economic_assessment,
            "assessment_criteria": ["cost_benefit", "budget_fit", "risk_return", "market_economics"]
        }
    
    def create_implementation_roadmap(self, feasible_solutions: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Create realistic implementation roadmap for feasible solutions."""
        
        roadmap_prompt = f"""
        Create a realistic implementation roadmap for these feasible solutions:
        
        Solutions to Implement:
        {json.dumps(feasible_solutions, indent=2)}
        
        Create a detailed roadmap with:
        
        1. PHASE 1 - IMMEDIATE ACTIONS (0-30 days):
        - Quick wins and low-hanging fruit
        - Minimal resource requirements
        - High impact, low effort initiatives
        - Foundation setting activities
        
        2. PHASE 2 - SHORT-TERM IMPLEMENTATION (1-6 months):
        - Medium complexity solutions
        - Moderate resource investment
        - Skill development and training
        - System setup and optimization
        
        3. PHASE 3 - LONG-TERM SCALING (6+ months):
        - Complex implementations
        - Significant resource commitment
        - Advanced features and capabilities
        - Expansion and scaling activities
        
        For each phase, provide:
        - Specific tasks and milestones
        - Resource requirements
        - Success metrics
        - Risk mitigation strategies
        - Dependencies and prerequisites
        
        Consider Indian business environment and SME constraints.
        """
        
        implementation_roadmap = self.generate_response(roadmap_prompt)
        
        return {
            "implementation_roadmap": implementation_roadmap,
            "total_phases": 3,
            "timeline": "12+ months"
        }
    
    def receive_message(self, message: AgentMessage):
        """Handle messages from other agents."""
        
        if message.message_type == "request":
            # Process feasibility assessment request
            task = message.content
            context = message.context or {}
            
            # Conduct feasibility analysis
            feasibility_results = self.process_request(task, context)
            
            # Send results back to Master Agent
            self.send_message_to_agent(
                recipient="Master Agent",
                message_type="response",
                content=feasibility_results,
                context={"original_request": task}
            )
            
            # Update shared state with feasibility analysis
            self.update_shared_state("feasibility_analysis", feasibility_results)
    
    def process_request(self, request: str, context: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """Process a feasibility assessment request."""
        
        try:
            # Get data from other agents
            research_data = self.get_shared_state("research_results") or {}
            financial_data = self.get_shared_state("financial_analysis") or {}
            uiux_data = self.get_shared_state("uiux_analysis") or {}
            
            # Extract business context
            business_type = "local service business"
            if context:
                analysis = context.get("analysis", {})
                business_type = analysis.get("business_type", business_type)
            
            # Compile proposed solutions from other agents
            proposed_solutions = {
                "research_recommendations": research_data.get("summary", ""),
                "financial_strategies": financial_data.get("summary", ""),
                "uiux_improvements": uiux_data.get("summary", "")
            }
            
            business_context = {
                "business_type": business_type,
                "target_market": "India",
                "constraints": ["limited budget", "small team", "local market focus"]
            }
            
            # Conduct feasibility assessments
            feasibility_analysis = {
                "technical_feasibility": self.assess_technical_feasibility(proposed_solutions, business_context),
                "economic_feasibility": self.assess_economic_feasibility(
                    financial_data.get("financial_data", {}),
                    {"budget_range": "₹50,000 - ₹2,00,000", "timeline": "12 months"}
                ),
                "implementation_roadmap": self.create_implementation_roadmap([
                    {"solution": "Digital presence optimization", "priority": "high"},
                    {"solution": "Customer acquisition improvement", "priority": "high"},
                    {"solution": "Process automation", "priority": "medium"}
                ])
            }
            
            # Generate feasibility summary
            summary_prompt = f"""
            Based on the feasibility analysis, provide an executive summary for a {business_type}.
            
            Feasibility Analysis:
            {json.dumps(feasibility_analysis, indent=2)}
            
            Provide a structured feasibility summary with:
            1. Overall Feasibility Assessment
            2. High-Priority Feasible Solutions
            3. Implementation Challenges and Solutions
            4. Resource Requirements and Timeline
            5. Risk Assessment and Mitigation
            6. Success Probability and Recommendations
            
            Focus on practical, achievable recommendations for Indian SMEs.
            """
            
            feasibility_summary = self.generate_response(summary_prompt)
            
            return {
                "status": "success",
                "feasibility_data": feasibility_analysis,
                "summary": feasibility_summary,
                "business_type": business_type,
                "assessment_date": "current"
            }
            
        except Exception as e:
            return {
                "status": "error",
                "error": str(e),
                "message": "Error conducting feasibility analysis"
            }
