"""
Research Agent - Gathers and verifies external information.
"""

import json
from typing import Dict, Any, List, Optional
from .base_agent import BaseAgent
from ..utils.communication import AgentMessage


class ResearchAgent(BaseAgent):
    """
    Research Agent that gathers and verifies all external information.
    Critical for grounding the system in reality and preventing hallucinations.
    """
    
    def __init__(self, communication):
        super().__init__(
            name="Research Agent",
            description="Gathers and verifies external information using Google Search and web analysis, with focus on Indian market data and business intelligence",
            communication=communication
        )
    
    def get_specific_instructions(self) -> str:
        return """
        As the Research Agent, your responsibilities include:
        
        1. MARKET RESEARCH: Conduct comprehensive market analysis for Indian businesses
        2. COMPETITOR ANALYSIS: Research competitors and industry benchmarks
        3. DIGITAL PRESENCE ANALYSIS: Analyze websites, social media, and online presence
        4. TREND IDENTIFICATION: Identify relevant industry trends and opportunities
        5. DATA VERIFICATION: Ensure all information is factual and up-to-date
        6. SOURCE CITATION: Always provide sources for your findings
        
        RESEARCH PRIORITIES:
        - Focus on Indian market conditions and local business environment
        - Prioritize recent data (last 6-12 months)
        - Look for actionable insights, not just general information
        - Consider regional variations within India
        - Include both English and Hindi market insights where relevant
        
        SEARCH STRATEGIES:
        - Use specific Indian business terms and contexts
        - Include location-specific searches (city, state, region)
        - Research local competitors and market leaders
        - Look for Indian government data and industry reports
        - Consider cultural and seasonal factors
        
        Always verify information from multiple sources and flag any uncertainties.
        """
    
    def conduct_market_research(self, business_type: str, location: str = "India") -> Dict[str, Any]:
        """Conduct comprehensive market research for a business type."""
        
        research_queries = [
            f"{business_type} market size India 2024",
            f"{business_type} industry trends India",
            f"{business_type} competition analysis {location}",
            f"{business_type} customer behavior India",
            f"{business_type} pricing strategies India"
        ]
        
        research_results = {}
        
        for query in research_queries:
            result = self.search_information(
                query=query,
                context=f"Researching {business_type} market in {location}"
            )
            
            if result["status"] == "success":
                research_results[query] = {
                    "content": result["content"],
                    "metadata": result.get("metadata"),
                    "timestamp": result.get("timestamp")
                }
            else:
                research_results[query] = {"error": result.get("error")}
        
        return research_results
    
    def analyze_competitor_landscape(self, business_type: str, location: str = "India") -> Dict[str, Any]:
        """Analyze the competitive landscape for a business type."""
        
        competitor_queries = [
            f"top {business_type} companies India",
            f"best {business_type} services {location}",
            f"{business_type} market leaders India",
            f"{business_type} startup success stories India"
        ]
        
        competitor_analysis = {}
        
        for query in competitor_queries:
            result = self.search_information(
                query=query,
                context=f"Analyzing competitors for {business_type} in {location}"
            )
            
            if result["status"] == "success":
                competitor_analysis[query] = result["content"]
        
        return competitor_analysis
    
    def analyze_digital_presence(self, website_url: str = None, business_name: str = None) -> Dict[str, Any]:
        """Analyze digital presence of a business."""
        
        analysis_results = {}
        
        # Website analysis if URL provided
        if website_url:
            website_analysis = self.analyze_website(website_url)
            analysis_results["website"] = website_analysis
        
        # Search for business online presence
        if business_name:
            search_queries = [
                f"{business_name} reviews India",
                f"{business_name} social media presence",
                f"{business_name} Google My Business",
                f"{business_name} online reputation"
            ]
            
            online_presence = {}
            for query in search_queries:
                result = self.search_information(
                    query=query,
                    context=f"Analyzing online presence of {business_name}"
                )
                
                if result["status"] == "success":
                    online_presence[query] = result["content"]
            
            analysis_results["online_presence"] = online_presence
        
        return analysis_results
    
    def research_industry_trends(self, business_type: str) -> Dict[str, Any]:
        """Research current industry trends and opportunities."""
        
        trend_queries = [
            f"{business_type} trends 2024 India",
            f"{business_type} digital transformation India",
            f"{business_type} customer preferences India",
            f"{business_type} technology adoption India",
            f"{business_type} growth opportunities India"
        ]
        
        trend_analysis = {}
        
        for query in trend_queries:
            result = self.search_information(
                query=query,
                context=f"Researching trends for {business_type} industry"
            )
            
            if result["status"] == "success":
                trend_analysis[query] = result["content"]
        
        return trend_analysis
    
    def research_target_audience(self, business_type: str, location: str = "India") -> Dict[str, Any]:
        """Research target audience characteristics and behavior."""
        
        audience_queries = [
            f"{business_type} target audience India demographics",
            f"{business_type} customer behavior {location}",
            f"{business_type} customer pain points India",
            f"{business_type} customer acquisition strategies India"
        ]
        
        audience_research = {}
        
        for query in audience_queries:
            result = self.search_information(
                query=query,
                context=f"Researching target audience for {business_type}"
            )
            
            if result["status"] == "success":
                audience_research[query] = result["content"]
        
        return audience_research
    
    def receive_message(self, message: AgentMessage):
        """Handle messages from other agents."""
        
        if message.message_type == "request":
            # Process research request
            task = message.content
            context = message.context or {}
            
            # Extract business information from context
            business_type = context.get("analysis", {}).get("business_type", "local service business")
            user_query = context.get("user_query", "")
            
            # Conduct comprehensive research
            research_results = self.process_request(task, context)
            
            # Send results back to Master Agent
            self.send_message_to_agent(
                recipient="Master Agent",
                message_type="response",
                content=research_results,
                context={"original_request": task}
            )
            
            # Update shared state with research findings
            self.update_shared_state("research_results", research_results)
    
    def process_request(self, request: str, context: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """Process a research request and return comprehensive findings."""
        
        try:
            # Extract business information from context
            business_type = "local service business"
            location = "India"
            website_url = None
            business_name = None
            
            if context:
                analysis = context.get("analysis", {})
                business_type = analysis.get("business_type", business_type)
                
                # Try to extract business details from user query
                user_query = context.get("user_query", "")
                if "website" in user_query.lower() or "http" in user_query:
                    # Extract URL if present
                    words = user_query.split()
                    for word in words:
                        if word.startswith("http"):
                            website_url = word
                            break
            
            # Conduct comprehensive research
            research_results = {
                "market_research": self.conduct_market_research(business_type, location),
                "competitor_analysis": self.analyze_competitor_landscape(business_type, location),
                "industry_trends": self.research_industry_trends(business_type),
                "target_audience": self.research_target_audience(business_type, location)
            }
            
            # Add digital presence analysis if applicable
            if website_url or business_name:
                research_results["digital_presence"] = self.analyze_digital_presence(website_url, business_name)
            
            # Generate research summary
            summary_prompt = f"""
            Based on the comprehensive research conducted, provide a summary of key findings for a {business_type} in {location}.
            
            Research Data:
            {json.dumps(research_results, indent=2)}
            
            Provide a structured summary with:
            1. Market Overview
            2. Key Opportunities
            3. Competitive Landscape
            4. Target Audience Insights
            5. Industry Trends
            6. Actionable Recommendations
            
            Focus on insights that can drive business growth and ROI.
            """
            
            research_summary = self.generate_response(summary_prompt)
            
            return {
                "status": "success",
                "research_data": research_results,
                "summary": research_summary,
                "business_type": business_type,
                "location": location
            }
            
        except Exception as e:
            return {
                "status": "error",
                "error": str(e),
                "message": "Error conducting research analysis"
            }
