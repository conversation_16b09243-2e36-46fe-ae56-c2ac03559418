"""
Base agent class for Project Synapse.
"""

from abc import ABC, abstractmethod
from typing import Dict, Any, List, Optional
from google import genai
from google.genai import types
from ..utils.communication import AgentCommunication, AgentMessage
from ..utils.tools import GoogleSearchTool, WebBrowserTool
from ..utils.config import load_config


class BaseAgent(ABC):
    """Base class for all agents in Project Synapse."""
    
    def __init__(self, name: str, description: str, communication: AgentCommunication):
        self.name = name
        self.description = description
        self.communication = communication
        self.config = load_config()
        
        # Initialize Gemini client
        self.client = genai.Client()
        
        # Initialize tools
        self.search_tool = GoogleSearchTool(self.client)
        self.web_tool = WebBrowserTool()
        
        # Register with communication system
        self.communication.register_agent(self.name, self)
        
        # Agent-specific configuration
        self.model = self.config["gemini"]["model"]
        self.temperature = self.config["gemini"]["temperature"]
        self.thinking_budget = self.config["gemini"]["thinking_budget"]
        
        # Indian context
        self.indian_context = self.config["indian_context"]
    
    def get_system_instruction(self) -> str:
        """Get the system instruction for this agent."""
        base_instruction = f"""
        You are {self.name}, a specialized AI agent in Project Synapse, a multi-agent system 
        designed to provide strategic business analysis and growth plans for local service 
        businesses in India.
        
        Your role: {self.description}
        
        IMPORTANT CONTEXT:
        - Focus specifically on the Indian market and business environment
        - Consider local regulations, cultural factors, and market dynamics
        - Prioritize low-effort, high-impact solutions (80/20 principle)
        - All recommendations must be practical and actionable for Indian SMEs
        - Currency references should be in INR
        - Consider both English and Hindi-speaking customer bases
        
        You can communicate with other agents in the system and access shared information.
        Always provide factual, well-researched recommendations grounded in real data.
        """
        
        return base_instruction + self.get_specific_instructions()
    
    @abstractmethod
    def get_specific_instructions(self) -> str:
        """Get agent-specific instructions."""
        pass
    
    def generate_response(self, prompt: str, context: Optional[Dict[str, Any]] = None) -> str:
        """Generate a response using the Gemini model."""
        try:
            # Prepare context information
            context_info = ""
            if context:
                context_info = f"\nContext: {context}"
            
            # Get shared state for additional context
            shared_state = self.communication.get_shared_state()
            if shared_state:
                context_info += f"\nShared Information: {shared_state}"
            
            full_prompt = prompt + context_info
            
            config = types.GenerateContentConfig(
                system_instruction=self.get_system_instruction(),
                temperature=self.temperature,
                thinking_config=types.ThinkingConfig(thinking_budget=self.thinking_budget)
            )
            
            response = self.client.models.generate_content(
                model=self.model,
                contents=full_prompt,
                config=config
            )
            
            return response.text
            
        except Exception as e:
            return f"Error generating response: {str(e)}"
    
    def search_information(self, query: str, context: str = "") -> Dict[str, Any]:
        """Search for information using Google Search."""
        return self.search_tool.search(query, context)
    
    def analyze_website(self, url: str) -> Dict[str, Any]:
        """Analyze a website using the web browser tool."""
        return self.web_tool.fetch_url(url)
    
    def send_message_to_agent(self, recipient: str, message_type: str, 
                             content: Any, context: Optional[Dict[str, Any]] = None) -> str:
        """Send a message to another agent."""
        return self.communication.send_message(
            sender=self.name,
            recipient=recipient,
            message_type=message_type,
            content=content,
            context=context
        )
    
    def receive_message(self, message: AgentMessage):
        """Receive a message from another agent."""
        # Default implementation - can be overridden by specific agents
        print(f"{self.name} received message from {message.sender}: {message.content}")
    
    def update_shared_state(self, key: str, value: Any):
        """Update shared state."""
        self.communication.update_shared_state(key, value, self.name)
    
    def get_shared_state(self, key: Optional[str] = None) -> Any:
        """Get shared state."""
        return self.communication.get_shared_state(key)
    
    @abstractmethod
    def process_request(self, request: str, context: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """Process a request and return results."""
        pass
