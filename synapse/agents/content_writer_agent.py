"""
Human Content Writer Agent - Generates high-quality marketing content.
"""

import json
from typing import Dict, Any, Optional, List
from .base_agent import BaseAgent
from ..utils.communication import AgentMessage


class HumanContentWriterAgent(BaseAgent):
    """
    Human Content Writer Agent that generates high-quality, engaging content
    for marketing materials tailored to Indian audiences.
    """
    
    def __init__(self, communication):
        super().__init__(
            name="Human Content Writer Agent",
            description="Generates high-quality marketing content and messaging strategies for Indian audiences",
            communication=communication
        )
    
    def get_specific_instructions(self) -> str:
        return """
        As the Human Content Writer Agent, your responsibilities include:
        
        1. CONTENT STRATEGY: Develop comprehensive content marketing strategies
        2. COPYWRITING: Create compelling marketing copy and messaging
        3. LOCALIZATION: Adapt content for Indian cultural context
        4. MULTI-CHANNEL: Create content for various platforms and channels
        5. ENGAGEMENT: Focus on content that drives customer engagement
        6. CONVERSION: Write content that converts prospects to customers
        
        INDIAN AUDIENCE CONSIDERATIONS:
        - Cultural sensitivity and local references
        - Mix of English and Hindi where appropriate
        - Regional preferences and variations
        - Festival and seasonal content opportunities
        - Trust-building and credibility focus
        - Family and community-oriented messaging
        
        CONTENT TYPES TO CREATE:
        - Website copy and landing pages
        - Social media content and campaigns
        - Email marketing sequences
        - Blog posts and articles
        - WhatsApp marketing messages
        - Google Ads and Facebook Ads copy
        
        TONE AND STYLE:
        - Professional yet approachable
        - Trustworthy and credible
        - Clear and easy to understand
        - Action-oriented with strong CTAs
        - Culturally relevant and respectful
        - Value-focused messaging
        """
    
    def create_website_copy(self, business_type: str, value_propositions: List[str]) -> Dict[str, Any]:
        """Create compelling website copy for the business."""
        
        copy_prompt = f"""
        Create compelling website copy for a {business_type} targeting Indian customers.
        
        Value Propositions:
        {', '.join(value_propositions)}
        
        Create copy for:
        
        1. HOMEPAGE:
        - Compelling headline that captures attention
        - Subheadline explaining the value proposition
        - Hero section copy with clear CTA
        - Trust signals and social proof
        - Benefits-focused content
        
        2. ABOUT US PAGE:
        - Company story and mission
        - Team credentials and expertise
        - Local presence and community connection
        - Awards, certifications, testimonials
        
        3. SERVICES PAGE:
        - Service descriptions with benefits
        - Process explanation
        - Pricing transparency where appropriate
        - FAQ section
        - Contact and booking CTAs
        
        4. CONTACT PAGE:
        - Multiple contact options
        - Location and service area details
        - Response time commitments
        - WhatsApp integration messaging
        
        Requirements:
        - Use Indian English and local terminology
        - Include trust-building elements
        - Focus on customer benefits over features
        - Include strong, clear call-to-actions
        - Optimize for mobile reading
        - Include local SEO keywords naturally
        """
        
        website_copy = self.generate_response(copy_prompt)
        
        return {
            "website_copy": website_copy,
            "business_type": business_type,
            "target_audience": "Indian customers"
        }
    
    def create_social_media_strategy(self, business_type: str, platforms: List[str]) -> Dict[str, Any]:
        """Create social media content strategy and sample posts."""
        
        social_prompt = f"""
        Create a social media content strategy for a {business_type} on these platforms: {', '.join(platforms)}
        
        Develop strategy for:
        
        1. CONTENT PILLARS:
        - Educational content (40%)
        - Behind-the-scenes content (20%)
        - Customer testimonials and success stories (20%)
        - Promotional content (15%)
        - Community and cultural content (5%)
        
        2. PLATFORM-SPECIFIC CONTENT:
        
        For Facebook:
        - Community building posts
        - Local event participation
        - Customer testimonials with photos
        - Educational carousel posts
        - Live Q&A sessions
        
        For Instagram:
        - Before/after visual content
        - Stories with polls and questions
        - Reels showcasing services
        - User-generated content
        - Local hashtag strategies
        
        For WhatsApp Business:
        - Broadcast list strategies
        - Customer service templates
        - Promotional message formats
        - Status update ideas
        
        For LinkedIn (if B2B):
        - Industry insights and trends
        - Professional achievements
        - Networking content
        - Thought leadership posts
        
        3. CONTENT CALENDAR:
        - Weekly posting schedule
        - Festival and seasonal content
        - Local event tie-ins
        - Trending topic integration
        
        4. ENGAGEMENT TACTICS:
        - Community management approach
        - Response templates
        - User-generated content campaigns
        - Influencer collaboration ideas
        
        Include 10 sample posts for each platform with captions, hashtags, and posting times.
        """
        
        social_strategy = self.generate_response(social_prompt)
        
        return {
            "social_media_strategy": social_strategy,
            "platforms": platforms,
            "content_focus": "Indian_market_engagement"
        }
    
    def create_email_marketing_sequences(self, customer_journey_stages: List[str]) -> Dict[str, Any]:
        """Create email marketing sequences for different customer journey stages."""
        
        email_prompt = f"""
        Create email marketing sequences for these customer journey stages: {', '.join(customer_journey_stages)}
        
        Create sequences for:
        
        1. WELCOME SEQUENCE (New Subscribers):
        - Email 1: Welcome and introduction
        - Email 2: Company story and values
        - Email 3: Service overview and benefits
        - Email 4: Customer testimonials
        - Email 5: Special offer or consultation
        
        2. NURTURE SEQUENCE (Prospects):
        - Educational content emails
        - Case studies and success stories
        - Industry insights and tips
        - Social proof and testimonials
        - Soft promotional content
        
        3. CONVERSION SEQUENCE (Warm Leads):
        - Problem identification emails
        - Solution presentation
        - Urgency and scarcity elements
        - Strong call-to-action
        - Follow-up and objection handling
        
        4. RETENTION SEQUENCE (Existing Customers):
        - Thank you and onboarding
        - Service tips and best practices
        - Upsell and cross-sell opportunities
        - Referral program promotion
        - Loyalty and appreciation content
        
        For each email, provide:
        - Subject line (including Hindi options where appropriate)
        - Email body copy
        - Call-to-action
        - Personalization suggestions
        - Send timing recommendations
        
        Focus on:
        - Indian cultural context and festivals
        - Trust-building and relationship focus
        - Clear value proposition
        - Mobile-optimized formatting
        - Local language integration
        """
        
        email_sequences = self.generate_response(email_prompt)
        
        return {
            "email_sequences": email_sequences,
            "journey_stages": customer_journey_stages,
            "localization": "Indian_market"
        }
    
    def receive_message(self, message: AgentMessage):
        """Handle messages from other agents."""
        
        if message.message_type == "request":
            # Process content creation request
            task = message.content
            context = message.context or {}
            
            # Create content recommendations
            content_results = self.process_request(task, context)
            
            # Send results back to Master Agent
            self.send_message_to_agent(
                recipient="Master Agent",
                message_type="response",
                content=content_results,
                context={"original_request": task}
            )
            
            # Update shared state with content strategy
            self.update_shared_state("content_strategy", content_results)
    
    def process_request(self, request: str, context: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """Process a content creation request."""
        
        try:
            # Extract business information from context
            business_type = "local service business"
            if context:
                analysis = context.get("analysis", {})
                business_type = analysis.get("business_type", business_type)
            
            # Define value propositions and platforms
            value_propositions = [
                "Expert service with local knowledge",
                "Trusted by Indian customers",
                "Affordable and transparent pricing",
                "Quick response and reliable service",
                "Culturally sensitive approach"
            ]
            
            platforms = ["Facebook", "Instagram", "WhatsApp Business", "Google My Business"]
            customer_journey_stages = ["awareness", "consideration", "decision", "retention"]
            
            # Generate content strategy
            content_analysis = {
                "website_copy": self.create_website_copy(business_type, value_propositions),
                "social_media_strategy": self.create_social_media_strategy(business_type, platforms),
                "email_marketing": self.create_email_marketing_sequences(customer_journey_stages)
            }
            
            # Generate content summary
            summary_prompt = f"""
            Based on the content strategy analysis, provide recommendations for a {business_type}.
            
            Content Analysis:
            {json.dumps(content_analysis, indent=2)}
            
            Provide a structured content summary with:
            1. Content Marketing Strategy Overview
            2. Website Copy Recommendations
            3. Social Media Content Plan
            4. Email Marketing Strategy
            5. Content Calendar and Scheduling
            6. Performance Metrics and KPIs
            
            Focus on practical, culturally relevant content for Indian audiences.
            """
            
            content_summary = self.generate_response(summary_prompt)
            
            return {
                "status": "success",
                "content_data": content_analysis,
                "summary": content_summary,
                "business_type": business_type,
                "target_market": "India"
            }
            
        except Exception as e:
            return {
                "status": "error",
                "error": str(e),
                "message": "Error creating content strategy"
            }
