"""
Master Agent - Central coordinator for Project Synapse.
"""

import json
from typing import Dict, Any, List, Optional
from .base_agent import BaseAgent


class MasterAgent(BaseAgent):
    """
    Master Agent that coordinates all other agents and synthesizes final responses.
    """
    
    def __init__(self, communication):
        super().__init__(
            name="Master Agent",
            description="Central coordinator that parses user queries, delegates tasks to specialized agents, and synthesizes comprehensive business growth plans",
            communication=communication
        )
        
        # Available specialized agents
        self.available_agents = [
            "Research Agent",
            "Financial Agent", 
            "UI/UX Designer Agent",
            "Feasibility Agent",
            "Question Answering Agent",
            "AI Software Development Agent",
            "Human Content Writer Agent"
        ]
    
    def get_specific_instructions(self) -> str:
        return """
        As the Master Agent, your responsibilities include:
        
        1. QUERY ANALYSIS: Parse user queries about local service businesses in India
        2. TASK DECOMPOSITION: Break down complex requests into specific tasks for specialized agents
        3. AGENT ORCHESTRATION: Delegate tasks to appropriate agents based on their expertise
        4. COORDINATION: Manage the workflow and ensure agents have necessary information
        5. SYNTHESIS: Combine insights from all agents into a comprehensive, actionable business plan
        6. QUALITY ASSURANCE: Ensure all recommendations are practical for Indian SMEs
        
        WORKFLOW PROCESS:
        - Analyze the user's business and requirements
        - Identify which agents need to be involved
        - Coordinate information gathering and analysis
        - Synthesize findings into a structured multi-stage growth plan
        - Present clear, actionable recommendations with ROI projections
        
        OUTPUT FORMAT: Always provide final recommendations in markdown format with:
        - Executive Summary
        - Stage 1: Immediate Wins (0-30 days)
        - Stage 2: Foundational Growth (1-6 months) 
        - Stage 3: Scaling Up (6+ months)
        - ROI Projections and Success Metrics
        """
    
    def analyze_user_query(self, query: str) -> Dict[str, Any]:
        """Analyze user query and determine required agents and tasks."""
        
        analysis_prompt = f"""
        Analyze this business query and determine:
        1. What type of business analysis is needed
        2. Which specialized agents should be involved
        3. What specific tasks each agent should perform
        4. What information we need to gather
        
        User Query: {query}
        
        Available Agents:
        - Research Agent: Gathers market data, competitor analysis, online presence analysis
        - Financial Agent: ROI analysis, pricing strategies, financial projections
        - UI/UX Designer Agent: Website/app analysis, user experience recommendations
        - Feasibility Agent: Assesses practicality of proposed solutions
        - Question Answering Agent: Answers specific questions based on gathered data
        - AI Software Development Agent: Technical implementation recommendations
        - Human Content Writer Agent: Marketing content and messaging strategies
        
        Provide your analysis in JSON format with:
        {{
            "business_type": "identified business type",
            "analysis_needed": ["list of analysis types needed"],
            "agents_required": ["list of agent names"],
            "priority_tasks": ["list of high-priority tasks"],
            "information_gaps": ["what additional info we need from user"]
        }}
        """
        
        response = self.generate_response(analysis_prompt)
        
        try:
            # Try to extract JSON from response
            if "```json" in response:
                json_str = response.split("```json")[1].split("```")[0].strip()
            else:
                json_str = response
            
            analysis = json.loads(json_str)
            return analysis
        except:
            # Fallback if JSON parsing fails
            return {
                "business_type": "local service business",
                "analysis_needed": ["market research", "financial analysis", "digital presence"],
                "agents_required": ["Research Agent", "Financial Agent", "UI/UX Designer Agent"],
                "priority_tasks": ["analyze current business state", "identify growth opportunities"],
                "information_gaps": ["business details", "current marketing efforts", "target audience"]
            }
    
    def orchestrate_agents(self, analysis: Dict[str, Any], user_query: str, 
                          business_context: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """Orchestrate the work of specialized agents based on analysis."""
        
        results = {}
        
        # Store initial context in shared state
        self.update_shared_state("user_query", user_query)
        self.update_shared_state("business_analysis", analysis)
        if business_context:
            self.update_shared_state("business_context", business_context)
        
        # Send tasks to required agents
        for agent_name in analysis.get("agents_required", []):
            if agent_name in self.available_agents:
                
                # Prepare agent-specific task
                task = self.prepare_agent_task(agent_name, analysis, user_query)
                
                # Send task to agent
                message_id = self.send_message_to_agent(
                    recipient=agent_name,
                    message_type="request",
                    content=task,
                    context={"analysis": analysis, "user_query": user_query}
                )
                
                print(f"Sent task to {agent_name}: {task[:100]}...")
        
        return {"status": "agents_orchestrated", "message_ids": []}
    
    def prepare_agent_task(self, agent_name: str, analysis: Dict[str, Any], user_query: str) -> str:
        """Prepare specific task for each agent."""
        
        base_context = f"User Query: {user_query}\nBusiness Type: {analysis.get('business_type', 'Unknown')}"
        
        if agent_name == "Research Agent":
            return f"""
            {base_context}
            
            Please conduct comprehensive research on:
            1. Market analysis for this business type in India
            2. Competitor analysis and benchmarking
            3. Current digital presence analysis (if website/social media provided)
            4. Industry trends and opportunities
            5. Target audience insights for Indian market
            
            Focus on actionable insights for growth opportunities.
            """
        
        elif agent_name == "Financial Agent":
            return f"""
            {base_context}
            
            Please analyze:
            1. Revenue optimization opportunities
            2. Cost reduction strategies
            3. Pricing strategy recommendations for Indian market
            4. ROI projections for different growth strategies
            5. Budget allocation recommendations
            6. Financial metrics and KPIs to track
            
            Provide specific INR amounts and percentages where possible.
            """
        
        elif agent_name == "UI/UX Designer Agent":
            return f"""
            {base_context}
            
            Please analyze and recommend:
            1. Website/app user experience improvements
            2. Mobile optimization for Indian users
            3. Local language considerations
            4. User journey optimization
            5. Conversion rate optimization strategies
            6. Accessibility improvements
            
            Focus on practical, cost-effective improvements.
            """
        
        elif agent_name == "Feasibility Agent":
            return f"""
            {base_context}
            
            Please assess:
            1. Feasibility of proposed growth strategies
            2. Resource requirements and constraints
            3. Timeline estimates for implementation
            4. Risk assessment and mitigation strategies
            5. Technical requirements and capabilities needed
            6. Regulatory considerations for Indian market
            
            Provide realistic implementation roadmaps.
            """
        
        elif agent_name == "Human Content Writer Agent":
            return f"""
            {base_context}
            
            Please create:
            1. Marketing messaging strategy for Indian audience
            2. Content calendar recommendations
            3. Social media content ideas
            4. Website copy improvements
            5. Local language content strategy
            6. Customer communication templates
            
            Focus on culturally relevant, engaging content.
            """
        
        elif agent_name == "AI Software Development Agent":
            return f"""
            {base_context}
            
            Please recommend:
            1. Technology solutions for business automation
            2. AI-powered tools for customer service
            3. Digital transformation opportunities
            4. Integration possibilities with existing systems
            5. Cost-effective tech stack recommendations
            6. Implementation priorities and phases
            
            Focus on practical, affordable solutions for SMEs.
            """
        
        else:
            return f"{base_context}\n\nPlease provide analysis and recommendations based on your expertise."
    
    def synthesize_final_plan(self, user_query: str) -> str:
        """Synthesize insights from all agents into a comprehensive business plan."""
        
        # Get all shared state information
        shared_state = self.get_shared_state()
        
        # Get recent messages from agents
        recent_messages = self.communication.get_conversation_context(limit=20)
        
        synthesis_prompt = f"""
        Based on the analysis from all specialized agents, create a comprehensive business growth plan.
        
        Original User Query: {user_query}
        
        Available Information:
        {json.dumps(shared_state, indent=2)}
        
        Recent Agent Communications:
        {json.dumps(recent_messages, indent=2)}
        
        Create a detailed business growth plan in markdown format with:
        
        # Business Growth Plan for [Business Name]
        
        ## Executive Summary
        - Brief overview of current state
        - Key opportunities identified
        - Expected outcomes
        
        ## Stage 1: Immediate Wins (0-30 days)
        - Quick, low-cost improvements
        - Expected ROI and timeline
        - Specific action items
        
        ## Stage 2: Foundational Growth (1-6 months)
        - Medium-term strategies
        - Investment requirements
        - Implementation roadmap
        
        ## Stage 3: Scaling Up (6+ months)
        - Long-term growth strategies
        - Advanced implementations
        - Expansion opportunities
        
        ## ROI Projections and Success Metrics
        - Financial projections in INR
        - Key performance indicators
        - Success measurement criteria
        
        ## Implementation Support
        - Resource requirements
        - Potential challenges and solutions
        - Next steps
        
        Focus on practical, actionable recommendations specifically tailored for the Indian market.
        """
        
        return self.generate_response(synthesis_prompt)
    
    def process_request(self, request: str, context: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """Process a user request and coordinate the full analysis."""
        
        try:
            # Step 1: Analyze the query
            analysis = self.analyze_user_query(request)
            
            # Step 2: Check if we need more information
            if analysis.get("information_gaps"):
                return {
                    "status": "need_more_info",
                    "analysis": analysis,
                    "questions": analysis["information_gaps"],
                    "message": "I need some additional information to provide the best recommendations."
                }
            
            # Step 3: Orchestrate agents
            orchestration_result = self.orchestrate_agents(analysis, request, context)
            
            # Step 4: For now, return analysis (agents will be implemented next)
            return {
                "status": "analysis_complete",
                "analysis": analysis,
                "orchestration": orchestration_result,
                "message": "Analysis complete. Specialized agents are working on your request."
            }
            
        except Exception as e:
            return {
                "status": "error",
                "error": str(e),
                "message": "An error occurred while processing your request."
            }
