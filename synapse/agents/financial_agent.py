"""
Financial Agent - Analyzes financial implications and ROI projections.
"""

import json
from typing import Dict, Any, List, Optional
from .base_agent import BaseAgent
from ..utils.communication import AgentMessage


class FinancialAgent(BaseAgent):
    """
    Financial Agent that analyzes financial implications of proposed strategies
    and provides ROI projections with Indian market context.
    """
    
    def __init__(self, communication):
        super().__init__(
            name="Financial Agent",
            description="Analyzes financial implications, ROI projections, pricing strategies, and budget optimization for Indian businesses",
            communication=communication
        )
    
    def get_specific_instructions(self) -> str:
        return """
        As the Financial Agent, your responsibilities include:
        
        1. ROI ANALYSIS: Calculate return on investment for proposed strategies
        2. PRICING STRATEGY: Recommend optimal pricing for Indian market
        3. BUDGET OPTIMIZATION: Allocate resources for maximum impact
        4. FINANCIAL PROJECTIONS: Create realistic revenue and cost forecasts
        5. COST-BENEFIT ANALYSIS: Evaluate financial viability of recommendations
        6. CASH FLOW PLANNING: Ensure sustainable financial operations
        
        INDIAN MARKET CONSIDERATIONS:
        - All amounts should be in Indian Rupees (INR)
        - Consider local purchasing power and price sensitivity
        - Factor in GST and other Indian tax implications
        - Account for seasonal business variations
        - Consider regional economic differences within India
        - Include digital payment adoption trends
        
        FINANCIAL METRICS TO FOCUS ON:
        - Customer Acquisition Cost (CAC) in INR
        - Customer Lifetime Value (CLV) in INR
        - Monthly Recurring Revenue (MRR) growth
        - Gross margin and net profit margins
        - Break-even analysis and payback periods
        - Working capital requirements
        
        Always provide specific INR amounts, percentages, and timelines.
        Focus on practical, achievable financial targets for Indian SMEs.
        """
    
    def analyze_pricing_strategy(self, business_type: str, market_data: Dict[str, Any] = None) -> Dict[str, Any]:
        """Analyze and recommend pricing strategies for Indian market."""
        
        pricing_prompt = f"""
        Analyze pricing strategy for a {business_type} in India.
        
        Market Data Available:
        {json.dumps(market_data, indent=2) if market_data else "No specific market data provided"}
        
        Provide pricing analysis including:
        1. Competitive pricing benchmarks in INR
        2. Value-based pricing recommendations
        3. Psychological pricing strategies for Indian consumers
        4. Tiered pricing options (basic, premium, enterprise)
        5. Seasonal pricing adjustments
        6. Regional pricing variations within India
        7. Digital vs traditional service pricing
        
        Consider:
        - Local purchasing power
        - Price sensitivity in Indian market
        - GST implications (18% for most services)
        - Competition pricing
        - Value perception
        
        Provide specific INR amounts and percentage margins.
        """
        
        pricing_analysis = self.generate_response(pricing_prompt)
        
        return {
            "pricing_strategy": pricing_analysis,
            "currency": "INR",
            "market_context": "India"
        }
    
    def calculate_roi_projections(self, investment_amount: float, strategy_type: str, 
                                 timeframe_months: int = 12) -> Dict[str, Any]:
        """Calculate ROI projections for different strategies."""
        
        roi_prompt = f"""
        Calculate ROI projections for a {strategy_type} strategy with an investment of ₹{investment_amount:,.0f} over {timeframe_months} months.
        
        Provide detailed financial projections including:
        
        1. REVENUE PROJECTIONS:
        - Month-by-month revenue growth
        - Customer acquisition numbers
        - Average transaction value in INR
        - Repeat customer rate
        
        2. COST BREAKDOWN:
        - Initial setup costs
        - Monthly operational costs
        - Marketing and advertising costs
        - Technology and tool costs
        - Staff/contractor costs
        
        3. ROI CALCULATIONS:
        - Monthly ROI percentages
        - Break-even point (month)
        - Total ROI at 6 months and 12 months
        - Net profit projections
        
        4. RISK FACTORS:
        - Best case scenario (+20% performance)
        - Worst case scenario (-20% performance)
        - Most likely scenario (baseline)
        
        Use realistic assumptions for Indian SME market conditions.
        All amounts in INR with proper formatting (₹1,00,000 format).
        """
        
        roi_analysis = self.generate_response(roi_prompt)
        
        return {
            "roi_projections": roi_analysis,
            "investment_amount": investment_amount,
            "timeframe_months": timeframe_months,
            "currency": "INR"
        }
    
    def analyze_budget_allocation(self, total_budget: float, business_goals: List[str]) -> Dict[str, Any]:
        """Recommend optimal budget allocation across different strategies."""
        
        budget_prompt = f"""
        Optimize budget allocation for a total budget of ₹{total_budget:,.0f} across these business goals:
        {', '.join(business_goals)}
        
        Provide detailed budget allocation with:
        
        1. PRIORITY-BASED ALLOCATION:
        - High-impact, low-cost initiatives (40-50% of budget)
        - Medium-impact, medium-cost strategies (30-40% of budget)
        - High-impact, high-cost investments (10-20% of budget)
        
        2. CATEGORY BREAKDOWN:
        - Digital marketing and advertising
        - Technology and tools
        - Content creation and branding
        - Staff training and development
        - Infrastructure improvements
        - Emergency/contingency fund (10-15%)
        
        3. TIMELINE ALLOCATION:
        - Immediate actions (Month 1-2): X% of budget
        - Short-term strategies (Month 3-6): Y% of budget
        - Long-term investments (Month 7-12): Z% of budget
        
        4. ROI EXPECTATIONS:
        - Expected return for each category
        - Payback period for each investment
        - Risk assessment for each allocation
        
        Focus on 80/20 principle - maximum impact with minimum investment.
        All amounts in INR with Indian market context.
        """
        
        budget_analysis = self.generate_response(budget_prompt)
        
        return {
            "budget_allocation": budget_analysis,
            "total_budget": total_budget,
            "goals": business_goals,
            "currency": "INR"
        }
    
    def calculate_customer_metrics(self, business_type: str, market_data: Dict[str, Any] = None) -> Dict[str, Any]:
        """Calculate key customer financial metrics."""
        
        metrics_prompt = f"""
        Calculate key customer financial metrics for a {business_type} in India.
        
        Market Context:
        {json.dumps(market_data, indent=2) if market_data else "General Indian market assumptions"}
        
        Calculate and provide:
        
        1. CUSTOMER ACQUISITION COST (CAC):
        - Digital marketing CAC
        - Referral CAC
        - Traditional marketing CAC
        - Blended CAC across all channels
        
        2. CUSTOMER LIFETIME VALUE (CLV):
        - Average transaction value
        - Purchase frequency per year
        - Customer retention rate
        - Average customer lifespan
        - CLV calculation in INR
        
        3. KEY RATIOS:
        - CLV:CAC ratio (should be 3:1 or higher)
        - Payback period for customer acquisition
        - Monthly churn rate
        - Revenue per customer per month
        
        4. GROWTH METRICS:
        - Monthly recurring revenue (MRR) potential
        - Annual recurring revenue (ARR) potential
        - Customer growth rate targets
        - Revenue growth rate targets
        
        Use realistic assumptions for Indian market:
        - Consider price sensitivity
        - Factor in payment method preferences
        - Account for seasonal variations
        - Include regional differences
        
        All amounts in INR with proper Indian number formatting.
        """
        
        metrics_analysis = self.generate_response(metrics_prompt)
        
        return {
            "customer_metrics": metrics_analysis,
            "business_type": business_type,
            "currency": "INR"
        }
    
    def receive_message(self, message: AgentMessage):
        """Handle messages from other agents."""
        
        if message.message_type == "request":
            # Process financial analysis request
            task = message.content
            context = message.context or {}
            
            # Conduct financial analysis
            financial_results = self.process_request(task, context)
            
            # Send results back to Master Agent
            self.send_message_to_agent(
                recipient="Master Agent",
                message_type="response",
                content=financial_results,
                context={"original_request": task}
            )
            
            # Update shared state with financial analysis
            self.update_shared_state("financial_analysis", financial_results)
    
    def process_request(self, request: str, context: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """Process a financial analysis request."""
        
        try:
            # Extract business information from context
            business_type = "local service business"
            if context:
                analysis = context.get("analysis", {})
                business_type = analysis.get("business_type", business_type)
            
            # Get market data from shared state if available
            market_data = self.get_shared_state("research_results")
            
            # Conduct comprehensive financial analysis
            financial_analysis = {
                "pricing_strategy": self.analyze_pricing_strategy(business_type, market_data),
                "customer_metrics": self.calculate_customer_metrics(business_type, market_data),
                "roi_projections": {
                    "digital_marketing": self.calculate_roi_projections(50000, "digital marketing", 12),
                    "website_optimization": self.calculate_roi_projections(25000, "website optimization", 6),
                    "social_media": self.calculate_roi_projections(15000, "social media marketing", 12)
                },
                "budget_allocation": self.analyze_budget_allocation(
                    100000, 
                    ["increase online presence", "improve customer acquisition", "enhance customer retention"]
                )
            }
            
            # Generate financial summary
            summary_prompt = f"""
            Based on the comprehensive financial analysis, provide an executive summary for a {business_type}.
            
            Financial Analysis Data:
            {json.dumps(financial_analysis, indent=2)}
            
            Provide a structured financial summary with:
            1. Key Financial Opportunities
            2. Recommended Investment Priorities
            3. Expected ROI and Payback Periods
            4. Budget Allocation Strategy
            5. Financial Risk Assessment
            6. Success Metrics and KPIs
            
            Focus on actionable financial recommendations with specific INR amounts.
            """
            
            financial_summary = self.generate_response(summary_prompt)
            
            return {
                "status": "success",
                "financial_data": financial_analysis,
                "summary": financial_summary,
                "business_type": business_type,
                "currency": "INR"
            }
            
        except Exception as e:
            return {
                "status": "error",
                "error": str(e),
                "message": "Error conducting financial analysis"
            }
