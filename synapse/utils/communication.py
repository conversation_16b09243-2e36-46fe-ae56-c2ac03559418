"""
Inter-agent communication system for Project Synapse.
"""

import json
from typing import Dict, Any, List, Optional
from dataclasses import dataclass, asdict
from datetime import datetime


@dataclass
class AgentMessage:
    """Represents a message between agents."""
    sender: str
    recipient: str
    message_type: str  # 'request', 'response', 'notification', 'data'
    content: Any
    timestamp: str
    message_id: str
    context: Optional[Dict[str, Any]] = None


class AgentCommunication:
    """Manages communication between agents."""
    
    def __init__(self):
        self.message_history: List[AgentMessage] = []
        self.shared_state: Dict[str, Any] = {}
        self.agent_registry: Dict[str, Any] = {}
    
    def register_agent(self, agent_name: str, agent_instance: Any):
        """Register an agent in the communication system."""
        self.agent_registry[agent_name] = agent_instance
        print(f"Agent '{agent_name}' registered in communication system")
    
    def send_message(self, sender: str, recipient: str, message_type: str, 
                    content: Any, context: Optional[Dict[str, Any]] = None) -> str:
        """
        Send a message from one agent to another.
        
        Args:
            sender: Name of sending agent
            recipient: Name of receiving agent
            message_type: Type of message ('request', 'response', 'notification', 'data')
            content: Message content
            context: Additional context
            
        Returns:
            Message ID
        """
        message_id = f"{sender}_{recipient}_{len(self.message_history)}"
        
        message = AgentMessage(
            sender=sender,
            recipient=recipient,
            message_type=message_type,
            content=content,
            timestamp=datetime.now().isoformat(),
            message_id=message_id,
            context=context
        )
        
        self.message_history.append(message)
        
        # If recipient is registered, deliver message
        if recipient in self.agent_registry:
            try:
                recipient_agent = self.agent_registry[recipient]
                if hasattr(recipient_agent, 'receive_message'):
                    recipient_agent.receive_message(message)
            except Exception as e:
                print(f"Error delivering message to {recipient}: {e}")
        
        return message_id
    
    def get_messages_for_agent(self, agent_name: str, 
                              message_type: Optional[str] = None) -> List[AgentMessage]:
        """Get all messages for a specific agent."""
        messages = [msg for msg in self.message_history if msg.recipient == agent_name]
        
        if message_type:
            messages = [msg for msg in messages if msg.message_type == message_type]
        
        return messages
    
    def update_shared_state(self, key: str, value: Any, agent_name: str):
        """Update shared state that all agents can access."""
        self.shared_state[key] = {
            "value": value,
            "updated_by": agent_name,
            "timestamp": datetime.now().isoformat()
        }
    
    def get_shared_state(self, key: Optional[str] = None) -> Any:
        """Get shared state value(s)."""
        if key:
            return self.shared_state.get(key, {}).get("value")
        return {k: v["value"] for k, v in self.shared_state.items()}
    
    def get_conversation_context(self, limit: int = 10) -> List[Dict[str, Any]]:
        """Get recent conversation context for agents."""
        recent_messages = self.message_history[-limit:] if limit > 0 else self.message_history
        return [asdict(msg) for msg in recent_messages]
    
    def clear_history(self):
        """Clear message history (useful for testing)."""
        self.message_history.clear()
        self.shared_state.clear()
