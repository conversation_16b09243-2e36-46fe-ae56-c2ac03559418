"""
Configuration management for Project Synapse.
"""

import os
from typing import Dict, Any
from dotenv import load_dotenv

def load_config() -> Dict[str, Any]:
    """Load configuration from environment variables."""
    load_dotenv()
    
    return {
        "google_cloud": {
            "project": os.getenv("GOOGLE_CLOUD_PROJECT", "my-first-project"),
            "location": os.getenv("GOOGLE_CLOUD_LOCATION", "us-central1"),
            "use_vertexai": os.getenv("GOOGLE_GENAI_USE_VERTEXAI", "True").lower() == "true"
        },
        "gemini": {
            "api_key": os.getenv("GEMINI_API_KEY"),
            "model": "gemini-2.5-flash",
            "temperature": 0.1,
            "thinking_budget": -1  # Dynamic thinking
        },
        "project": {
            "name": os.getenv("PROJECT_NAME", "Project Synapse"),
            "environment": os.getenv("ENVIRONMENT", "development")
        },
        "indian_context": {
            "market_focus": "India",
            "business_types": ["local service businesses", "SMEs", "startups"],
            "languages": ["English", "Hindi"],
            "currency": "INR"
        }
    }
