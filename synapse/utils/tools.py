"""
Tools for agents to interact with external systems.
"""

import requests
from typing import Dict, Any, List
from bs4 import BeautifulSoup
from google import genai
from google.genai import types


class GoogleSearchTool:
    """Tool for performing Google searches with grounding."""
    
    def __init__(self, client: genai.Client):
        self.client = client
        self.grounding_tool = types.Tool(
            google_search=types.GoogleSearch()
        )
    
    def search(self, query: str, context: str = "") -> Dict[str, Any]:
        """
        Perform a Google search with grounding.
        
        Args:
            query: Search query
            context: Additional context for the search
            
        Returns:
            Dictionary containing search results and metadata
        """
        try:
            config = types.GenerateContentConfig(
                tools=[self.grounding_tool]
            )
            
            search_prompt = f"""
            Search for information about: {query}
            
            Context: {context}
            
            Focus on information relevant to Indian businesses and market conditions.
            Provide factual, up-to-date information with sources.
            """
            
            response = self.client.models.generate_content(
                model="gemini-2.5-flash",
                contents=search_prompt,
                config=config
            )
            
            return {
                "status": "success",
                "content": response.text,
                "metadata": getattr(response, 'grounding_metadata', None)
            }
            
        except Exception as e:
            return {
                "status": "error",
                "error": str(e),
                "content": None
            }


class WebBrowserTool:
    """Tool for browsing and analyzing web pages."""
    
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        })
    
    def fetch_url(self, url: str) -> Dict[str, Any]:
        """
        Fetch and analyze content from a URL.
        
        Args:
            url: URL to fetch
            
        Returns:
            Dictionary containing page content and analysis
        """
        try:
            response = self.session.get(url, timeout=10)
            response.raise_for_status()
            
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # Extract key information
            title = soup.find('title')
            title_text = title.get_text().strip() if title else "No title"
            
            # Remove script and style elements
            for script in soup(["script", "style"]):
                script.decompose()
            
            # Get text content
            text_content = soup.get_text()
            lines = (line.strip() for line in text_content.splitlines())
            chunks = (phrase.strip() for line in lines for phrase in line.split("  "))
            text = ' '.join(chunk for chunk in chunks if chunk)
            
            # Limit content length
            if len(text) > 5000:
                text = text[:5000] + "..."
            
            return {
                "status": "success",
                "url": url,
                "title": title_text,
                "content": text,
                "length": len(text)
            }
            
        except Exception as e:
            return {
                "status": "error",
                "url": url,
                "error": str(e),
                "content": None
            }
