"""
Main ADK agent for Project Synapse.
This is the entry point for the ADK framework.
"""

from google.adk import Agent
from typing import Dict, Any, Optional
import json

# Import our multi-agent system
from .utils.communication import AgentCommunication
from .utils.config import load_config
from .agents import (
    MasterAgent,
    ResearchAgent,
    FinancialAgent,
    UIUXDesignerAgent,
    FeasibilityAgent,
    QuestionAnsweringAgent,
    AISoftwareDevelopmentAgent,
    HumanContentWriterAgent
)


class ProjectSynapseSystem:
    """
    Project Synapse Multi-Agent System
    Coordinates all specialized agents for business analysis.
    """
    
    def __init__(self):
        self.config = load_config()
        self.communication = AgentCommunication()
        
        # Initialize all agents
        self.master_agent = MasterAgent(self.communication)
        self.research_agent = ResearchAgent(self.communication)
        self.financial_agent = FinancialAgent(self.communication)
        self.uiux_agent = UIUXDesignerAgent(self.communication)
        self.feasibility_agent = FeasibilityAgent(self.communication)
        self.qa_agent = QuestionAnsweringAgent(self.communication)
        self.ai_dev_agent = AISoftwareDevelopmentAgent(self.communication)
        self.content_agent = HumanContentWriterAgent(self.communication)
        
        print("Project Synapse Multi-Agent System initialized successfully!")
        print(f"Registered agents: {list(self.communication.agent_registry.keys())}")
    
    def process_business_query(self, query: str, context: Optional[Dict[str, Any]] = None) -> str:
        """
        Process a business query through the multi-agent system.

        Args:
            query: User's business query
            context: Additional context information

        Returns:
            Comprehensive business analysis and recommendations
        """
        try:
            # Step 1: Master Agent analyzes the query
            print(f"Processing query: {query[:100]}...")

            analysis_result = self.master_agent.process_request(query, context)

            if analysis_result["status"] == "need_more_info":
                return self._format_info_request(analysis_result)

            # Step 2: Simulate agent coordination and processing
            print("Agents are analyzing your business...")

            # Simulate research agent work
            research_context = {"analysis": analysis_result.get("analysis", {}), "user_query": query}
            research_result = self.research_agent.process_request("Conduct comprehensive market research", research_context)

            # Simulate financial agent work
            financial_result = self.financial_agent.process_request("Analyze financial opportunities", research_context)

            # Simulate UI/UX agent work
            uiux_result = self.uiux_agent.process_request("Analyze user experience opportunities", research_context)

            # Simulate feasibility agent work
            feasibility_result = self.feasibility_agent.process_request("Assess implementation feasibility", research_context)

            # Simulate technical agent work
            tech_result = self.ai_dev_agent.process_request("Recommend technical solutions", research_context)

            # Simulate content agent work
            content_result = self.content_agent.process_request("Create content strategy", research_context)

            print("All agents have completed their analysis. Synthesizing final plan...")

            # Step 3: Generate final comprehensive plan
            final_plan = self.master_agent.synthesize_final_plan(query)

            return final_plan

        except Exception as e:
            return f"Error processing query: {str(e)}\n\nPlease check your Google Cloud configuration and ensure you have proper authentication set up."
    
    def _format_info_request(self, analysis_result: Dict[str, Any]) -> str:
        """Format a request for additional information."""
        
        questions = analysis_result.get("questions", [])
        analysis = analysis_result.get("analysis", {})
        
        response = f"""
# Additional Information Needed

I've analyzed your request and identified that you're looking for help with a **{analysis.get('business_type', 'business')}**.

To provide the most accurate and actionable recommendations, I need some additional information:

## Please provide details about:

"""
        
        for i, question in enumerate(questions, 1):
            response += f"{i}. {question}\n"
        
        response += """
## What I can help you with:

Based on my analysis, I can provide recommendations for:
"""
        
        for need in analysis.get("analysis_needed", []):
            response += f"- {need.title()}\n"
        
        response += """
Once you provide this information, my team of specialized AI agents will:
- Conduct comprehensive market research
- Analyze financial opportunities and ROI
- Assess technical feasibility
- Create implementation roadmaps
- Develop content and marketing strategies

Please share the additional details so I can create a comprehensive business growth plan for you!
"""
        
        return response


# ADK Tool Functions
def analyze_business(business_description: str, website_url: str = "", budget_range: str = "") -> str:
    """
    Analyze a business and provide growth recommendations.
    
    Args:
        business_description: Description of the business and what help is needed
        website_url: Optional website URL for analysis
        budget_range: Optional budget range for recommendations
        
    Returns:
        Comprehensive business analysis and growth plan
    """
    
    # Initialize the multi-agent system
    synapse_system = ProjectSynapseSystem()
    
    # Prepare context
    context = {}
    if website_url:
        context["website_url"] = website_url
    if budget_range:
        context["budget_range"] = budget_range
    
    # Process the business query
    result = synapse_system.process_business_query(business_description, context)
    
    return result


def ask_question(question: str, context: str = "") -> str:
    """
    Ask a specific question about business strategy or implementation.
    
    Args:
        question: Specific question to ask
        context: Additional context for the question
        
    Returns:
        Detailed answer based on available analysis
    """
    
    # Initialize the system
    synapse_system = ProjectSynapseSystem()
    
    # Use the QA agent to answer the question
    qa_result = synapse_system.qa_agent.process_request(
        f"Question: {question}",
        {"context": context}
    )
    
    if qa_result["status"] == "success":
        return qa_result["qa_data"]["answer"]
    else:
        return f"Error answering question: {qa_result.get('error', 'Unknown error')}"


# ADK Agent Definition
root_agent = Agent(
    name="project_synapse",
    model="gemini-2.5-flash",
    description="""
    Project Synapse is a sophisticated multi-agent AI system designed to provide strategic 
    business analysis and actionable growth plans for local service businesses in India.
    
    I coordinate a team of specialized AI agents including:
    - Research Agent: Market analysis and competitor research
    - Financial Agent: ROI analysis and budget optimization
    - UI/UX Designer Agent: Website and user experience optimization
    - Feasibility Agent: Implementation viability assessment
    - AI Development Agent: Technical solutions and automation
    - Content Writer Agent: Marketing content and messaging
    - Question Answering Agent: Specific query resolution
    
    I focus on the Indian market and provide practical, actionable recommendations 
    tailored to local business conditions and cultural context.
    """,
    instruction="""
    You are Project Synapse, a multi-agent AI system for business analysis and growth planning.
    
    Your primary role is to:
    1. Understand the user's business and requirements
    2. Coordinate specialized agents to conduct comprehensive analysis
    3. Synthesize insights into actionable growth plans
    4. Focus on the Indian market context and SME constraints
    5. Provide practical, cost-effective recommendations
    
    Always:
    - Ask clarifying questions when needed
    - Provide specific, actionable advice
    - Consider Indian market conditions and cultural factors
    - Focus on ROI and practical implementation
    - Use INR for financial projections
    - Suggest phased implementation approaches
    
    When users ask for business analysis, use the analyze_business tool.
    When users ask specific questions, use the ask_question tool.
    """,
    tools=[analyze_business, ask_question]
)
