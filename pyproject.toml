[tool.poetry]
name = "project-synapse"
version = "0.1.0"
description = "Multi-agent AI system for strategic business analysis and growth planning for local service businesses in India"
authors = ["Your Name <<EMAIL>>"]
readme = "README.md"
packages = [{include = "synapse"}]

[tool.poetry.dependencies]
python = "^3.11"
google-adk = "^0.1.0"
google-genai = "^0.8.0"
google-cloud-aiplatform = "^1.60.0"
pydantic = "^2.8.0"
python-dotenv = "^1.0.0"
requests = "^2.32.0"
beautifulsoup4 = "^4.12.0"
markdown = "^3.6.0"

[tool.poetry.group.dev.dependencies]
pytest = "^8.3.0"
black = "^24.0.0"
isort = "^5.13.0"
flake8 = "^7.1.0"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"
